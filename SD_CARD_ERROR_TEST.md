# SD卡错误处理测试文档

## 修改概述

我们已经成功实现了SD卡错误处理的双模式机制：

### 错误码定义
- `HW_ERROR_CODE_NO_SD_CARD = 90` - SD卡未检测到
- `HW_ERROR_CODE_READ_WRITE_FAIL = 92` - SD卡读写失败

### 实现的功能

#### 1. SD卡未检测到错误 (HW_ERROR_CODE_NO_SD_CARD)
**触发条件：**
- `Constants.getExternalConfigPath()` 返回 null
- SD卡检测重试机制失败

**发送的事件：**
- 事件名称：`sdcard_not_detected_error`
- 错误类型：`no_sd_card`
- 错误码：1
- 硬件错误码：90

#### 2. SD卡读写失败错误 (HW_ERROR_CODE_READ_WRITE_FAIL)
**触发条件：**
- SD卡检测到但无法创建目录
- SD卡检测到但文件写入失败
- SD卡相关的IOException

**发送的事件：**
- 事件名称：`sdcard_read_write_error`
- 错误类型：`read_write_fail`
- 错误码：2
- 硬件错误码：92

## 测试场景

### 测试1：SD卡未插入
1. 确保设备没有插入SD卡
2. 启动ConfigTool应用
3. 触发需要SD卡的操作
4. 预期结果：收到HW_ERROR_CODE_NO_SD_CARD错误事件

### 测试2：SD卡插入但写保护
1. 插入写保护的SD卡
2. 启动ConfigTool应用
3. 尝试保存配置到SD卡
4. 预期结果：收到HW_ERROR_CODE_READ_WRITE_FAIL错误事件

### 测试3：SD卡空间不足
1. 插入空间不足的SD卡
2. 启动ConfigTool应用
3. 尝试保存大文件到SD卡
4. 预期结果：收到HW_ERROR_CODE_READ_WRITE_FAIL错误事件

## 代码修改位置

### ConfigToolService.java
- 重构了 `sendSdCardNotInsertedError()` 为 `sendSdCardError(int hwErrorCode)`
- 在 `scheduleUnifiedSdCardRetry()` 中调用 `sendSdCardError(Constants.HW_ERROR_CODE_NO_SD_CARD)`
- 在 `backupEncryptedConfigToExternal()` 中调用 `sendSdCardError(Constants.HW_ERROR_CODE_READ_WRITE_FAIL)`
- 在 `writeStringToFile()` 中添加SD卡写入错误检测

### Constants.java
- 确保包含所有必要的硬件错误码定义

### MainActivity.java
- 改进了错误日志，便于调试

## 验证方法

1. 查看logcat日志，搜索关键词：
   - "Sending SD card not detected error"
   - "Sending SD card read/write error"
   - "SD card error event sent successfully"

2. 检查EventBroker事件：
   - 事件名称应该正确对应错误类型
   - 硬件错误码应该正确传递

3. 确认错误处理逻辑：
   - SD卡未检测到时不应该尝试写入操作
   - SD卡写入失败时应该有适当的重试机制
