/*
 * Copyright (C) 2025 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 */

package com.thundercomm.configtool.utils;

import android.content.Context;
import android.os.Environment;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.util.Log;
import java.io.File;
import java.util.List;

/**
 * Constants class to store all application constants
 */
public class Constants {
    private static final String TAG = "ConfigTool_Constants";

    // File names
    public static final String ENCRYPTED_CONFIG_FILENAME = "config.bin";
    public static final String EXTERNAL_CONFIG_FILENAME = "config.json";

    // Package name
    public static final String FILE_ENCRYPT_PACKAGE = "com.thundercomm.crown.fileencrypt";

    // Permission check removed, no longer needed

    // Broadcast actions
    public static final String CONFIG_CHANGED_ACTION = "com.thundercomm.configtool.CONFIG_CHANGED";

    // External storage path - cached after first detection
    private static String sExternalConfigPath = null;

    /**
     * Get external config path with lazy initialization.
     * @return External config path or null if SD card not available
     */
    public static String getExternalConfigPath() {
        if (sExternalConfigPath == null) {
            sExternalConfigPath = detectExternalSdCard();
            if (sExternalConfigPath != null) {
                sExternalConfigPath = sExternalConfigPath + "/Config/";
                // Try to create directory
                try {
                    File configDir = new File(sExternalConfigPath);
                    if (!configDir.exists()) {
                        configDir.mkdirs();
                    }
                } catch (Exception es) {
                    Log.e(TAG, "Failed to create config directory: " + es.getMessage());
                }
            }
        }
        return sExternalConfigPath;
    }

    // Constants for buffer sizes and unit conversions
    public static final int DEFAULT_BUFFER_SIZE = 1024;
    public static final int BYTES_PER_KB = 1024;
    public static final int BYTES_PER_MB = 1024 * 1024;

    // Constants for retry and timeout values
    public static final int CONNECTION_RETRY_LIMIT = 3;
    public static final int CONNECTION_RETRY_DELAY_MS = 500;

    // Error codes
    public static final int ERROR_NO_CONFIG = -1;
    public static final int ERROR_KEY_NOT_FOUND = -2;

    // UI constants
    public static final int MAX_DISPLAY_TEXT_LENGTH = 50;
    public static final int TRUNCATED_TEXT_LENGTH = 47;

    // Timing constants
    public static final int CONFIG_LOAD_WAIT_TIME_MS = 8000;
    public static final long ONE_HOUR_IN_MILLISECONDS = 3600000L; // 1 hour = 60 * 60 * 1000 ms
    public static final int ONE_SECOND_IN_MILLISECONDS = 1000;
    public static final int TWO_SECONDS_IN_MILLISECONDS = 2000;
    public static final int THREE_SECONDS_IN_MILLISECONDS = 3000;
    public static final int FIVE_SECONDS_IN_MILLISECONDS = 5000;
    public static final int TEN_SECONDS_IN_MILLISECONDS = 10000;

    // Test configuration values
    public static final int TEST_EVENT_RECORDING_TIME_SECONDS = 180;

    // Hardware error codes
    public static final int HW_ERROR_CODE_NO_SD_CARD = 90;
    public static final int HW_ERROR_CODE_SD_CARD_UNAVAILABLE = 91;
    public static final int HW_ERROR_CODE_READ_WRITE_FAIL = 92;

    // Context holder for SD card detection
    private static Context sContext;

    /**
     * Initialize Constants with application context.
     * This should be called from Application.onCreate() or Service.onCreate().
     *
     * @param context Application context
     */
    public static void initialize(Context context) {
        sContext = context.getApplicationContext();
    }



    /**
     * Detect external removable SD card path.
     * Based on EventProvider's testSdCard implementation.
     *
     * @return SD card root path (e.g., "/storage/9EE7-1000"), or null if not found
     */
    private static String detectExternalSdCard() {
        Log.d(TAG, "Starting SD card detection");

        if (sContext == null) {
            Log.e(TAG, "Constants not initialized with context");
            return null;
        }

        try {
            StorageManager storageManager = (StorageManager) sContext.getSystemService(Context.STORAGE_SERVICE);
            if (storageManager == null) {
                Log.e(TAG, "StorageManager not available");
                return null;
            }

            // Get all storage volumes
            List<StorageVolume> volumes = storageManager.getStorageVolumes();
            Log.d(TAG, "Found " + volumes.size() + " storage volumes");

            StorageVolume removableVolume = null;

            // Debug: List all volumes
            for (int i = 0; i < volumes.size(); i++) {
                StorageVolume volume = volumes.get(i);
                Log.d(TAG, "Volume " + i + " - isRemovable: " + volume.isRemovable()
                    + ", isPrimary: " + volume.isPrimary()
                    + ", description: " + volume.getDescription(sContext));
            }

            // Find removable storage volume (external SD card)
            for (StorageVolume volume : volumes) {
                if (volume.isRemovable() && !volume.isPrimary()) {
                    removableVolume = volume;
                    Log.d(TAG, "Selected removable SD card: " + volume.getDescription(sContext));
                    break;
                }
            }

            if (removableVolume == null) {
                Log.e(TAG, "No removable SD card found");
                return null;
            }

            // Get SD card path
            String sdCardPath = removableVolume.getPath();
            Log.d(TAG, "SD card path: " + sdCardPath);

            if (sdCardPath == null) {
                Log.e(TAG, "SD card path is null");
                return null;
            }

            // Check mount state using StorageManager
            String state = storageManager.getVolumeState(sdCardPath);
            Log.d(TAG, "SD card mount state: " + state);

            if (!Environment.MEDIA_MOUNTED.equals(state)) {
                Log.e(TAG, "SD card not mounted, state: " + state);
                return null;
            }

            Log.i(TAG, "SUCCESS: Found external SD card: " + sdCardPath);
            return sdCardPath;

        } catch (Exception ex) {
            Log.e(TAG, "Exception during SD card detection: " + ex.getMessage(), ex);
            return null;
        }
    }
}