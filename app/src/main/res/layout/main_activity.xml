<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (C) 2023-2024 THUNDERCOMM TECHNOLOGY Co.,Ltd.
 * All Rights Reserved.
 * Confidential and Proprietary - Thundercomm Technology Co.,Ltd.
 *
 * Not a contribution.
-->
<LinearLayout
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <ScrollView
        android:layout_width="0dp"
        android:layout_weight="2"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_gravity="left"
            android:layout_marginLeft=""
            android:layout_height="match_parent"
            android:orientation="vertical"
            >
            <Button
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ICC Test"
                android:onClick="onIcTest"
                />
        </LinearLayout>
    </ScrollView>
    <ScrollView
        android:layout_width="0dp"
        android:layout_weight="2"
        android:layout_height="match_parent"
        >
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </ScrollView>
</LinearLayout>